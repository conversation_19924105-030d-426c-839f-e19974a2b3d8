<view wx:if="{{show}}" class="redeem-modal-overlay {{visible ? 'show' : ''}}" bindtap="onClose">
  <view class="redeem-modal-content" catchtap="onStopPropagation">
    <!-- 模态框头部 -->
    <view class="modal-header">
      <view class="modal-title">
        <text class="title-icon">🎫</text>
        <text class="title-text">使用兑换码</text>
      </view>
      <view class="modal-close" bindtap="onClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <!-- 兑换码输入 -->
    <view class="modal-body">
      <view class="input-section">
        <view class="input-label">请输入兑换码</view>
        <view class="input-wrapper">
          <input
            class="code-input"
            type="text"
            placeholder="请输入兑换码"
            value="{{inputCode}}"
            bindinput="onCodeInput"
            maxlength="20"
            auto-focus="{{show}}"
          />
        </view>
        <view class="input-tip">兑换码不区分大小写，系统会自动转换</view>
      </view>

      <!-- 获取兑换码 -->
      <view class="get-code-section">
        <view class="get-code-title">获取兑换码</view>
        <view class="get-code-btn" bindtap="onGoToStore">
          <text class="btn-icon">🛒</text>
          <text class="btn-text">前往积分商店</text>
          <text class="btn-arrow">›</text>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="tips-section">
        <view class="tips-title">使用说明</view>
        <view class="tips-content">
          <view class="tip-item">• 兑换码可以兑换VIP会员时长</view>
          <view class="tip-item">• VIP时长会累加到现有时长</view>
          <view class="tip-item">• 每个兑换码只能使用一次</view>
          <view class="tip-item">• 兑换码有有效期，请及时使用</view>
        </view>
      </view>
    </view>

    <!-- 模态框底部 -->
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="onClose">取消</button>
      <button
        class="redeem-btn {{(!inputCode || loading) ? 'disabled' : ''}}"
        bindtap="onRedeemCode"
        loading="{{loading}}"
      >
        {{loading ? '兑换中...' : '立即兑换'}}
      </button>
    </view>
  </view>
</view>
